# ===================================================================
# Step 0: Import necessary libraries
# This MUST be run first in any new Julia session.
# ===================================================================
using ITensors
using Plots
using Printf

# ===================================================================
# Step 1: Define the Hamiltonian and run DMRG
# ===================================================================

"""
  Get MPO of transverse field Ising model Hamiltonian with field strength h
"""
function tfimMPO(sites, h::Float64)
    N = length(sites)
    ampo = OpSum()
    for j in 1:(N - 1)
        ampo += -1, "Z", j, "Z", j + 1
    end
    for j in 1:N
        ampo += h, "X", j
    end
    return MPO(ampo, sites)
end

# --- DMRG Setup ---
N = 50
h = 1.0 # Critical point

# This is the line that caused the error. It works now because `using ITensors` is above.
sites = siteindex("S=1/2", N)
ψ1 = randomMPS(sites; linkdims=10)

# Define parameters for DMRG sweeps using the modern API
sweeps = Sweeps(15)
maxdim!(sweeps, 10, 20, 100, 100, 200)
cutoff!(sweeps, 1E-10)
noise!(sweeps, 1E-7, 1E-8, 0.0)

# --- Run DMRG ---
println("Running DMRG for TFIM with N = $N, h=$h (critical point)")
println("================================")
observer = DMRGObserver(["Sz"], sites; energy_tol=1E-7)
H = tfimMPO(sites, h)
energy, ψ_gs = dmrg(H, ψ1, sweeps; observer)

# --- Print Results ---
println("\nFinal ground state energy = $energy")
println("\nMeasurements of <Sz> after each sweep:")
for (i, Szs) in enumerate(measurements(observer)["Sz"])
    @printf "Sweep %d, <Σ Sz>/N = %.6f\n" i (sum(Szs) / N)
end


# ===================================================================
# Step 2: Calculate and Plot Entanglement Entropy
# ===================================================================
println("\nCalculating entanglement entropy...")
SvNs = Float64[]
Ls = 2:(N - 1)

for b in Ls
    # Use the much cleaner, built-in entropy function
    SvN = entropy(ψ_gs, b)
    push!(SvNs, SvN)
end

println("Plotting entropy vs. bond position...")
# To display the plot, you might need to be in an environment like VS Code or Jupyter,
# or have a plot pane open.
display(plot(Ls, SvNs, xlabel="Bond b", ylabel="S_vN", title="Entanglement Entropy", legend=false, marker=:circle))


