using ITensors
##
"""
  Get MPO of transverse field Ising model Hamiltonian with field strength h
"""
function tfimMPO(sites, h::Float64)
    # Input operator terms which define a Hamiltonian
    N = length(sites)
    ampo = OpSum()
    for j in 1:(N - 1)
        ampo += -1, "Z", j, "Z", j + 1
    end
    for j in 1:N
        ampo += h, "X", j
    end
    # Convert these terms to an MPO tensor network
    return MPO(ampo, sites)
end


N = 50
sites = siteinds("S=1/2", N)
ψ1 = randomMPS(sites; linkdims=10)

# define parameters for DMRG sweeps
sweeps = Sweeps(15)
setmaxdim!(sweeps, 10, 20, 100, 100, 200)
setcutoff!(sweeps, 1E-10)

#=
create observer which will measure Sᶻ at each
site during the dmrg sweeps and track energies after each sweep.
in addition it will stop the computation if energy converges within
1E-7 tolerance
=#
h = 1.0
println("\nRunning DMRG for TFIM with N = $N h=$h (critical point)")
println("================================")
Sz_observer = DMRGObserver(["Sz"], sites; energy_tol=1E-7)
H = tfimMPO(sites, h)
energy, ψ1 = dmrg(H, ψ1, sweeps; observer=Sz_observer)

for (i, Szs) in enumerate(measurements(Sz_observer)["Sz"])
    println("<Σ Sz> after sweep $i = ", sum(Szs) / N)
end

##
ψ = ψ1
SvNs = []
Ls = 2:N-1
for b = Ls
    # println(b)
    orthogonalize!(ψ, b)
    U,S,V = svd(ψ[b], (linkind(ψ, b-1), siteind(ψ,b)))
    SvN = 0.0
    for n=1:dim(S, 1)
        p = S[n,n]^2
        SvN -= p * log(p)
    end
    append!(SvNs,[SvN])
    @show b, SvN
end
##
using Plots
plot(Ls,SvNs)


##
b = 50
orthogonalize!(ψ2, b)
U,S,V = svd(ψ2[b], (linkind(ψ2, b-1), siteind(ψ2,b)))
SvN = 0.0
for n=1:dim(S, 1)
    p = S[n,n]^2
    global SvN -= p * log(p)
end
SvN
##


# ψ1ψ1 = deepcopy(ψ2);
##
N = 100
sites = siteinds("S=1/2", N)
ψ1ψ1 = randomMPS(sites)
ψ1ψ1[1:50] = ψ1; 
ψ1ψ1[51:100] = deepcopy(ψ1);
replace_siteinds!(ψ1ψ1,siteinds(ψ2))
overlap = inner(ψ1ψ1,ψ2)

##
# for j = 1:100
#     println("j = $j, $(siteind(ψ1ψ1,j)), $(siteind(ψ2,j))")
#     replaceinds!(ψ1ψ1[j],[siteind(ψ1ψ1,j)], [siteind(ψ2,j)])
# end
##
# function YH_replace_siteinds!(M1::MPS, M2::MPS)
#     for j in eachindex(M1)
#         replaceinds!(M1[j],[siteind(M1,j)], [siteind(M2,j)])
#     end
#     return M1
# end
# YH_replace_siteinds!(ψ1ψ1,ψ2); 
##
