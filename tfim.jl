# ===================================================================
# Step 0: Import necessary libraries
# This MUST be run first in any new Julia session.
# ===================================================================
using ITensors, ITensorMPS
using Plots
using Printf
##
# ===================================================================
# Step 1: Define the Hamiltonian and run DMRG
# ===================================================================

"""
  Get MPO of transverse field Ising model Hamiltonian with field strength h
"""
function tfimMPO(sites, hx::Float64, hz::Float64)
    N = length(sites)
    ampo = OpSum()
    for j in 1:2:(N - 1)
        ampo += -1, "Z", j, "Z", j + 1
    end
    for j in 2:2:(N - 1)
        ampo += -1.2, "Z", j, "Z", j + 1
    end
    for j in 1:N
        ampo += hx, "X", j
        ampo += hz*(-1)^(j+1) , "Z", j

    end
    return MPO(ampo, sites)
end

# --- DMRG Setup ---
N = 20
hx = 0.2

hzlist = LinRange(0.0, 3.0, 10)
sweeps = Sweeps(15)
maxdim!(sweeps, 10, 20, 100, 100, 200)
cutoff!(sweeps, 1E-10)
noise!(sweeps, 1E-7, 1E-8, 0.0)
# hz = 0.1

for hz in hzlist
# Create site indices for spin-1/2 system
sites = siteinds("S=1/2", N)

# states = [isodd(n) ? "Up" : "Dn" for n in 1:N]
# states = [ "Up" for n in 1:N]
# states = [ "Dn" for n in 1:N]

# ψ0 = MPS(sites, states)
ψ0 = randomMPS(sites; linkdims=10)
# Define parameters for DMRG sweeps using the modern API


# --- Run DMRG ---
println("Running DMRG for TFIM with N = $N, hx = $hx, hz = $hz")
println("================================")
Sz_Sx_observer = DMRGObserver(["Sz", "Sx"], sites; energy_tol=1E-7)

# observer = DMRGObserver(["Sz"], sites; energy_tol=1E-7)
H = tfimMPO(sites, hx, hz)
energy, ψ_gs = dmrg(H, ψ0, sweeps; observer = Sz_Sx_observer)

# --- Print Results ---
println("\nFinal ground state energy = $energy")
# Assuming N (the total number of sites) is defined outside the loop
num_odd_sites = ceil(Int, N / 2)
num_even_sites = floor(Int, N / 2)

println("Sweep | Avg Magnetization | Avg on Odd Sites  | Avg on Even Sites")
println("------|-------------------|-------------------|-------------------")
for (i, Szs) in enumerate(measurements(Sz_Sx_observer)["Sz"])
    # Calculate the average magnetization on the whole chain
    avg_Sz_total = sum(Szs) / N
    # Calculate the average magnetization ON each sublattice
    # This is often more physically meaningful than the original calculation.
    avg_Sz_odd = sum(Szs[1:2:end]) / num_odd_sites
    avg_Sz_even = sum(Szs[2:2:end]) / num_even_sites
    avg_SZ_total = sum(Szs) / N

    # Print the results in a nicely formatted table
    @printf " %-4d | %+.15f | %+.15f | %+.15f\n" i avg_Sz_total avg_Sz_odd avg_Sz_even
end
for (i, Sxs) in enumerate(measurements(Sz_Sx_observer)["Sx"])
    println("<Σ Sx> after sweep $i = ", sum(Sxs) / N)
end

end

